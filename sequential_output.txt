📄 SEQUENTIAL PDF PROCESSING RESULTS
============================================================


==================== PAGE 1 ====================

[TEXT] AAI AIMS Test Application Server - Manual DR Drill Process

[TEXT] I. Stop Application Server at DC

[TEXT] 1.   Check Weblogic process status before stopping to ensure Weblogic process are running

[TEXT] ps -ef | grep wls

[TEXT] ps -ef | grep Node

[IMAGE - Page 1]
Image saved: sequential_output\page_1_image_1.png
Size: (1366, 485)
OCR Text:
-ef

[xoot@hyddrweblogic-test bin]J# ps

|

grep wls

root

17173

i

0 23:39 pts/O

00:00:01 /usr/java/jdk1.8.0_144/bin/java -Dderby.system.home=/Middleware/OracleHome/user projects/domains/AIMS domain/common/db —

classpath /Middleware/OracleHome/wiserver/common/derby/lib/derby. jar: /Middleware/OracleHome/wiserver/common/derby/lib/derbynet. jar: /Middleware/OracleHome/wiserver/commo

n/derby/lib/derbytools.jar: /Middleware/OracleHome/wiserver/common/derby/lib/derbyclient.jar org.apache.derby.drda.NetworkServerControl start

root

17186 17129 42 23:39 pts/0O

00:01:15 /usr/java/jdk1.8.0_144/bin/java -server -Xms256m -Xmx512m -cp /Middleware/OracleHome/wiserver/server/lib/weblogic—launch

ex.jar -Dlaunch.use.env.classpath=true -Dweblogic.Name=AdminServer -Djava.security.policy=/Middleware/OracleHome/wiserver/server/lib/weblogic.policy -Dweblogic. Producti

onModeEnabled=true -Djava.system.class.loader=com.oracle.classloader.weblogic.LaunchClassLoader -javaagent:/Middleware/OracleHome/wiserver/server/lib/debugpatch-agent.j

ax -da -Dwis.home=/Middleware/OracleHome/wiserver/server -Dweblogic.home=/Middleware/OracleHome/wiserver/server weblogic.Server

root

18329 18280 99 23:42 pts/O

00:00:53 /usr/java/jdk1.8.0_144/bin/java -server -Xms256m -Xmx51l2m -cp /Middleware/OracleHome/wiserver/server/lib/weblogic—launch

ex.jar -Dlaunch.use.env.classpath=true -Dweblogic.Name=FMSTEST -Djava.security.policy=/Middleware/OracleHome/wiserver/server/lib/weblogic.policy -Dweblogic.ProductionMo

deEnabled=true -Djava.system.class.loader=com.oracle.classloader.weblogic.LaunchClassLoader -javaagent:/Middleware/OracleHome/wiserver/server/lib/debugpatch-agent.jar -

da -Dwlis.home=/Middleware/OracleHome/wiserver/server -Dweblogic.home=/Middleware/OracleHome/wiserver/server -Dweblogic.management.server=http://weblogic-test:7001 weblo

gic.Server

root

18544 18543

@ 23:42 pts/O

00:00:00 /bin/sh /Middleware/OracleHome/wiserver/server/bin/startNodeManager.sh

root

18591 18544 58 23:42 pts/0O

00:00:05 /usr/java/jdk1.8.0_144/bin/java -server -Xms32m -Xmx200m -Djdk.tls.ephemeralDHKeySize=2048 -Dcoherence.home=/Middleware/

OracleHome/wiserver/../coherence —Dbea.home=/Middleware/OracleHome/wiserver/.. —-Dweblogic.RootDirectory—/Middleware/OracleHome/user_projects/domains/AIMS domain -Djava.

system.class.loader=com. oracle.classloader.weblogic.LaunchClassLoader -Djava.security.policy=/Middleware/OracleHome/wiserver/server/lib/weblogic.policy -Dweblogic.nodem

anager . JavaHome=/usr/java/jdk1.8.0_144 weblogic.NodeManager -v

root

18674 13152

@ 23:42 pts/O

GG:00:00 grep —--color=auto wis

[xoot@hyddrweblogic-test bin]J# ps

-ef

|

grep Node

root

18543 13152

@ 23:42 pts/O

00:00:00 /bin/sh ./startNodeManager.sh

root

18544 18543

@ 23:42 pts/O

00:00:00 /bin/sh /Middleware/OracleHome/wlserver/server/bin/startNodeManager.sh

root

18591 18544 40 23:42 pts/0O

00:00:05 /usr/java/jdk1.8.0_144/bin/java -server -Xms32m -Xmx200m -Djdk.tls.ephemeralDHKeySize=2048 -Dcoherence.home=/Middleware/

OracleHome/wlserver/../coherence -Dbea.home=/Middleware/OracleHome/wlserver/.. —Dwablogic.RootDirectory=/Middleware/OracleHome/user_projects/domains/AIMS domain -Djava.

system.class.loader=com.oracle.classloader.weblogic.LaunchClassLoader -Djava.secur

y-policy=/Middleware/OracleHome/wlserver/server/lib/weblogic.policy -Dweblogic.nodem

anager . JavaHome=/usr/java/jdk1.8.0_144 weblogic.NodeManager -v

root

18700 13152

@ 23:42 pts/O

GG:00:06 grep --color=auto Node

[root@hyddrweblogic-test bin]# I


==================================================

==================== PAGE 2 ====================

[TEXT] 2.   Stop Managed Server – 4 Managed Server’s

[TEXT] nohup ./stopManagedWebLogic.sh Test1 http://weblogic-test:7001 &

[TEXT] nohup ./stopManagedWebLogic.sh Test2 http://weblogic-test:7001 &

[TEXT] nohup ./stopManagedWebLogic.sh FMSNEW http://weblogic-test:7001 &

[TEXT] nohup ./stopManagedWebLogic.sh IDGTEST http://weblogic-test:7001 &

[IMAGE - Page 2]
Image saved: sequential_output\page_2_image_1.png
Size: (1366, 719)
OCR Text:
Initializing WebLogic Scripting Tool (WLST)

Welcome to WebLogic Server Administration Scripting Shell

Type help{) for help on available commands

Connecting to t3://weblogic-test:7001 with userid weblogic

Successfully connected to Admin Server "AdminServer" that belongs

to domain

"AIMS domain”

Warning: An insecure protocol was used to connect to the server

To ensure on-the-wire security,

the SSL port or Admin port should

be used instead

Shutting

down

the server IDGIEST with force-false while connected

to AdminServer

<Mar

14

2022

ii

33

55,544

PM

IST>

<Notice>

<Server> <BEA-002638>

<Graceful shutdown of IDGITEST was issued remotely from ***********.>

<Mar

14

2022

ii

33

55,565

PM

IST>

<Notice>

<Server> <BEA-002638>

<Graceful shutdown was issued remotely from ***********:7661.>

<Mar

14

2022

ii

33

55,568

PM

IST>

<Notice>

<WebLogicServer>

<BEA-GG0396>

<Server shutdown has been requested by weblogic.>

<Mar

14

2022

ii

33

55,570

PM

IST>

<Notice>

<WebLogicServer>

<BEA-GG0365>

<Server state changed to SUSPENDING.>

<Mar

14

2022

ii

33

55,635

PM

IST>

<Notice>

<WebLogicServer>

<BEA-GG0365>

<Server state changed to ADMIN.>

<Mar

14

2022

ii

33

55,637

PM

IST>

<Notice>

<WebLogicServer>

<BEA-GG0365>

<Server state changed to SHUTTING DOWN.>

<Mar

14

2022

ii

33

55,672

PM

IST>

<Notice>

<BEA-170037>

<Log Management>

<The log monitoring service timer has been stopped.>

<Mar

14

2022

ii

33

55,818

PM

IST>

<Warning> <JMX> <BEA-149513> <JMX Connector Server stopped at service:jmx:iiop://***********:7005/jndi/weblogic.management .mbeanserve

rs.runtime.>

<Mar 14

2022

ii

33

56,245

PM

IST>

<Notice> <JMX> <BEA-149535> <JMX Resiliency Activity Server=IDGTEST

Received a DISCONNECT EVENT

>

<Mar 14

2022

ii

33

56,247

PM

IST>

<Notice> <JMX> <BEA-149535> <JMX Resiliency Activity Server=IDGTEST

Attempting to reconnect to the server in DomainRuntime Service>

<Mar 14

2022

ii

33

56,247

PM

IST>

<Notice> <JMX> <BEA-149535> <JMX Resiliency Activity Server=IDGTEST

Attempting reconnection>

<Mar 14

2022

ii

33

56,248

PM

IST>

<Notice> <JMX> <BEA-149535> <JMX Resiliency Activity Se

ex=-IDGTEST

Starting JMX connection

forceReconnect value

true>

<Mar 14

2022

ii

33

56,260

PM

IST>

as unable to

<Warning> <JMX> <BEA-149564> <The Administration Server

establish JMX Connectivity with the IDGTEST at the JMX Service

URL of service:jmx:t3://16

io

96.14:7005/jndi/weblogic.management .mbeanservers.runtime.>

<Mar

14

2022

ii

34

60,248

PM

IST>

<Notice> <JMX> <BEA-149535> <JMX Resiliency Activity Server=IDGTEST

Failed to reconnect after 3 attempts,

disconnecting >

<Mar

14

2022

ii

34

60,249

PM

IST>

<Notice> <JMX> <BEA-149535> <JMX Resiliency Activity Server=IDGTEST

Cleaning up disconnected server>

<Mar

14

2022

ii

34

00,252

PM

IST>

<Notice> <JMX> <BEA-149535> <JMX Resiliency Activity Server=IDGTEST

Removing MBeanServerConnection in DomainRuntimeServiceMBean>

<Mar

14

2022

ii

34

60,253

PM

IST>

<Warning> <JMX> <BEA-149567> <JMX Connectivity has been discontinued with the Managed Server IDGIEST.>

Exiting WebLogic

Scripting

Tool

Done

Stopping Derby Server

Derby server stopped

: |

[TEXT] 3.   Stop Admin Server

[TEXT] nohup ./stopWebLogic.sh &

[IMAGE - Page 2]
Image saved: sequential_output\page_2_image_2.png
Size: (1350, 731)
OCR Text:
Stopping Derby Server...

Derby server stopped.

Stopping Weblogic Server...

Initializing WebLogic Scripting Tool ({WLST)

nee

Welcome to WebLogic Server Administration Scripting Shell

Type help{) for help on available commands

Connecting to t3://weblogic-test:7001 with userid weblogic

nee

Successfully connected to Admin Server "AdminServer” that belongs to domain "AIMS domain”.

Warning: An insecure protocol was used to connect to the server.

To ensure on-the-wire security,

the SSL port or Admin port should be used instead.

the server AdminServer with force-false while connected to AdminServer

nee

Shutting down

<Mar

14,

2022

11:34:52,523

PM

IST>

<Notice>

<Server> <BEA-002638> <Graceful shutdown of AdminServer was issued remotely from ***********.>

<Mar

14,

2022

11:34:52,528

PM

IST>

<Notice>

<WebLogicServer> <BEA-0G0G396>

<Server shutdown has been requested by weblogic.>

<Mar

14,

2022

11:34:52,529

PM

IST>

<Notice>

<WebLogicServer> <BEA-0GG365>

<Server state changed to SUSPENDING.>

<Mar

14,

2022

11:34:52,563

PM

IST>

<Notice>

<WebLogicServer, <BEA-000365>

<Server state changed to ADMIN.>

<Mar

2022

PM

IST>

<Notice>

14,

11:34:52,564

<WebLogicServer> <BEA-0GGG365>

<Server state changed to SHUITING DOWN.>

<Mar

14,

2022

11:34:52,593

PM

IST>

<Notice>

<Log Management> <BEA-170037>

<The log monitoring service timer has been stopped.>

<Mar

14,

2022

11:34:52,772

PM

IST>

<Warning> <JMX> <BEA-149513> <JMX Connector Server stopped at service:jmx:iiop://weblogic-test:7001/jndi/weblogic.management .mbeanser

vers.domainruntime.>

<Mar 14,

2022 11:34:52,788

PM

IST>

<Warning> <JMX> <BEA-149513> <JMX Connector Server stopped at service:jmx:iiop://weblogic-test:7001/jndi/weblogic.management .mbeanser

vers.edit.>

<Mar 14,

2022 11:34:52,819

PM

IST>

<Warning> <JMX> <BEA-149513> <JMX Connector Server stopped at service:jmx:iiop://weblogic-test:7001/jndi/weblogic.management .mbeanser

vers.runtime.>

Stopping Derby server...

Derby server stopped.

WLST lost connection to the WebLogic Server that you were connected to.

This may happen if the server was shut down or

partitioned.

You will have to re-connect to the server once

the server is available.

Disconnected from weblogic server: AdminServer

Disconnected from weblogic server: AdminServer

Exiting WebLogic Scripting Tool.

Done

Stopping Derby Server...

Derby server stopped.

i


==================================================

==================== PAGE 3 ====================

[TEXT] 4.   Stop Node Manager

[TEXT] nohup ./stopNodeManager.sh &

[IMAGE - Page 3]
Image saved: sequential_output\page_3_image_1.png
Size: (1350, 715)
OCR Text:
NodeManagerHome=/Middleware/OracleHome/user_projects/domains/AIMS domain/nodemanager

RestEnabled=false

weblogic.startup. JavaHome=/usr/java/jdk1.8.0_ 144

weblogic.startup.MW_ Home=

coherence. startup. JavaHome=/usr/java/jdk1.8.0_ 144

coherence.startup.MW_Home=

Domain name mappings:

AIMS domain -> /Middleware/OracleHome/user_projects/domains/AIMS domain

<Mar 14,

2022 11:35:33 PM IST> <INFO> <********.0>

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <Testl> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS domain/serv

ers/Testl/data/nodemanager/startup.properties"™>

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <Test2> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS domain/serv

ers/Test2/data/nodemanager/startup.properties">

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <SkyRevServer> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS doma

in/servers/SkyRevServer/data/nodemanager/startup.properties">

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <ITCHQServer> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS domai

n/servers/ITCHOServer/data/nodemanager/startup.properties">

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <GSTEINVLIVE> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS domai

n/servers/GSTEINVLIVE/data/nodemanager/startup.properties">

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <FMSTEST> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS domain/se

rvers/FMSTEST/data/nodemanager/startup.properties">

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <IDGTEST> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS domain/se

rvers/IDGTEST/data/nodemanager/startup.properties">

<Mar 14,

2022 11:35:33 PM

IST> <INFO> <Server Implementation Class: weblogic.nodemanager.server .NMServer$ClassicServer.>

<Mar 14,

2022 11:35:33 PM

IST> <INFO> <Plain socket listener started on port 5556, host weblogic-test/***********>

<2022-03-14 IST 23:36:04>

<Info> <StopNodeManager>

<NODEMGR_HOME is already set to /Middleware/OracleHome/user_projects/domains/AIMS domain/nodemanager>

<2022-03-14 IST 23:36:04>

<Info> <StopNodeManager>

<Begin>

<2022-03-14 IST 23:36:04>

<Info> <StopNodeManager>

<Adding NodeManagerHome entry to JAVA_OPTIONS: -DNodeManagerHome=/Middleware/OracleHome/user_projects/domains/AIMS do

main/nodemanager>

<2022-03-14 IST 23:36:04>

<Info> <StopNodeManager>

<Root Directory already set /Middleware/OracleHome/user_projects/domains/AIMS domain>

<2022-03-14 IST 23:36:04>

<Info> <StopNodeManager>

<Found process id from /Middleware/OracleHome/user_projects/domains/AIMS domain/nodemanager/nodemanager.process.id: 1

5451>

<2022-03-14 IST 23:36:04>

<Info> <StopNodeManager>

<Sending signal TERM to 15451>

+ status=143

+ set +x

/Middleware/OracleHome/user_projects/domains/AIMS domain/bin

./startNodeManager.sh: line 33:

-Dweblogic.security.SSL.ignoreHostnameVerification=true:

command not found

<2022-03-14 IST 23:36:05> <Info> <StopNodeManager> <End>

Ptiddleware/OracleHome/user_projects/domains/AIMS domain/nodemanager/stopnodemanager .1log was copied to /Middleware/OracleHome/user projects/domains/AIMS domain/nodemana

ger/nodemanager.log

[TEXT] 5.   Check Weblogic process status after stopping to ensure Weblogic processes are halted.

[TEXT] ps -ef | grep wls

[TEXT] ps -ef | grep Node

[IMAGE - Page 3]
Image saved: sequential_output\page_3_image_2.png
Size: (774, 178)
OCR Text:
[xroot@hyddrweblogic-test bin])# ps -ef

|

grep wls

root

16596 13152

0 23:38 pts/0

00:00:00 grep --color=auto wls

[xroot@hyddrweblogic-test bin])# ps -ef

|

grep Node

root

16618 13152

0 23:38 pts/0

00:00:00 grep --color=auto Nede

[xroot@hyddrweblogic-test bin]# I


==================================================

==================== PAGE 4 ====================

[TEXT] II. Start Application server at DR

[TEXT] 1.   Check Weblogic process status before starting to ensure weblogic processes are not

[TEXT] already running.

[TEXT] ps -ef | grep wls

[TEXT] ps -ef | grep Node

[IMAGE - Page 4]
Image saved: sequential_output\page_4_image_1.png
Size: (774, 178)
OCR Text:
[xroot@hyddrweblogic-test bin])# ps -ef

|

grep wls

root

16596 13152

0 23:38 pts/0

00:00:00 grep --color=auto wls

[xroot@hyddrweblogic-test bin])# ps -ef

|

grep Node

root

16618 13152

0 23:38 pts/0

00:00:00 grep --color=auto Nede

[xroot@hyddrweblogic-test bin]# I

[TEXT] 2.   Start Node Manager

[TEXT] nohup ./startNodeManager.sh &

[IMAGE - Page 4]
Image saved: sequential_output\page_4_image_2.png
Size: (1354, 396)
OCR Text:
[root@hyddrweblogic-test bin]#

nohup ./startNodeManager.sh &

[1] 15403

[root@hyddrweblogic-test bin]#

nohup:

ignoring input and appending output to

‘nohup.out’”

[root@hyddrweblogic-test bin]#

tail -f nohup.out

2022 11:35:33 PM IST>

<INFO> <********.0>

<Mar 14,

<Mar 14,

2022 11:35:33 PM IST>

<INFO> <AIMS_domain> <Testl> <Startup configuration properties loaded from "/Middleware/OracleHome/user projects/domains/AIMS domain/serv

ers/Testl/data/nodemanager/startup.properties">

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <Test2> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS domain/serv

ers/Test2/data/nodemanager/startup.properties">

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <SkyRevServer> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS doma

in/servers/SkyRevServer/data/nodemanager/startup.properties">

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <ITCHQServer> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS domai

n/servers/ITCHOQServer/data/nodemanager/startup.properties">

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <GSTEINVLIVE> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS domai

n/servers/GSTEINVLIVE/data/nodemanager/startup.properties">

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <FMSTEST> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS domain/se

rvers/FMSTEST/data/nodemanager/startup.properties">

<Mar 14, 2022 11:35:33 PM IST> <INFO> <AIMS domain> <IDGTEST> <Startup configuration properties loaded from "/Middleware/OracleHome/user_projects/domains/AIMS domain/se

rvers/IDGTEST/data/nodemanager/startup.properties">

<Mar 14,

2022 11:35:33 PM IST> <INFO> <Server Implementation Class: weblogic.nodemanager. server .NMServer$ClassicServer.>

<Mar 14,

2022 11:35:33 PM IST> <INFO> <Plain socket listener started on port 5556, host weblogic-test/***********>

i


==================================================

==================== PAGE 5 ====================

[TEXT] 3.   Start Admin Server

[TEXT] nohup ./startWebLogic.sh &

[IMAGE - Page 5]
Image saved: sequential_output\page_5_image_1.png
Size: (1349, 727)
OCR Text:
[oracle@hyddrweblogic-test bin]$ nohup ./startWebLogic.sh «€

[1] 9377

[oracle@hyddrweblogic-test bin]$ nohup:

ignoring input and appending output to

‘nohup.out’”

[oracle@hyddrweblogic-test bin]$ tail -f nohup.out

<Mar 14,

2022 11:23:15 PM IST> <Info> <RCM> <BEA-2165021> <"ResourceManagement” is not enabled in this JVM. Enable "ResourceManagement” to use the WebLogic Server "Reso

urce Consumption Management” feature.

To enable "ResourceManagement”,

you must specify the following JVM options in the WebLogic Server instance in which the JVM runs:

-XX:4+UnlockCommercialFeatures -XX:+ResourceManagement.>

<Mar 14,

2022 11:23:15 PM IST>

<Info> <Management> <BEA-141107> <Version: WebLogic Server ********.0 Thu Aug 17 13:39:49 PDI 2017 1882952>

<Mar 14,

2022 11:23:16 PM IST>

<Info> <Management> <BEA-141227> <Making a backup copy of the configuration at /Middleware/OracleHome/user_ projects/domains/AIMS domain/c

onfig-original.jar.>

<Mar 14,

2022 11:23:16 PM IST>

<Notice> <WebLogicServer> <BEA-G00365> <Server state changed to STARTING.>

2022 11:23:16 PM IST>

<Mar 14,

<Info> <WorkManager> <BEA-002900> <Initializing self-tuning thread pool.>

<Mar 14,

2022 11:23:16 PM IST>

<Info> <WorkManager> <BEA-002942> <CMM memory level becomes 0G.

Setting standby thread pool size to 256.>

<Mar 14,

2022 11:23:16,767 PM IST> <Notice> <Log Management> <BEA-170019> <The server log file weblogic.logging.FileStreamHandler instance=295434315

Current log file=/Middleware/OracleHome/user_projects/domains/AIMS domain/servers/AdminServer/logs/AdminServer.log

Rotation dir=/Middleware/OracleHome/user_projects/domains/AIMS domain/servers/AdminServer/logs

be written to this file.>

is opened. All server side log events will

<Mar

14,

2022

11:23:17,125

PM

IST>

<Notice>

<Security> <BEA-090946>

<Security pre-initializing using security realm:

myrealm>

<Mar

14,

2022

11:23:17,441

PM

IST>

<Notice>

<Security> <BEA-090947>

<Security post-initializing

using security realm:

myrealm>

<Mar

14,

2022

11:23:18,590

PM

IST>

<Notice>

<Security> <BEA-090082>

<Security initialized using

administrative security realm:

myrealm>

<Mar

14,

2022

11:23:19,045

PM

IST>

<Notice>

<JMX> <BEA-149512> <JMX

Connector Server started at

service:jmx:iiop://weblogic-test:7001/jndi/weblogic.management .mbeanserv

ers.runtime.>

<Mar 14,

2022

11:23:19,175

PM

IST>

<Notice>

<JMX> <BEA-149512> <JMX

Connector Server started at

service:jmx:iiop://weblogic-test:7001/jndi/weblogic.management .mbeanserv

ers.edit.>

<Mar 14,

2022

11:23:19,183

PM

IST>

<Notice>

<JMX> <BEA-149512> <JMX

Connector Server started at

service:jmx:iiop://weblogic-test:7001/jndi/weblogic.management .mbeanserv

ers.domainruntime.>

2022

PM

IST>

<Notice>

<BEA-000365>

to STANDBY.>

<Mar 14,

11:23:20,071

<WebLogicServer>

<Server state changed

<Mar 14,

2022

11:23:20,071

PM

IST>

<Notice>

<WebLogicServer>

<BEA-000365>

<Server state changed

to STARTING.>

<Mar 14,

2022

11:23:20,125

PM

IST>

<Notice>

<Log Management>

<BEA-170036>

<The Logging monitoring service timer has started to check for logged message counts every 30

seconds.>

<Mar 14,

2022

11:23:21,861

PM

IST>

<Notice>

<Log Management>

<The server has successfully established a connection with the Domain level Diagnostic Service

>

<BRA-170027>

<Mar 14,

2022

11:23:22,707

PM

IST>

<Notice>

<WebLogicServer>

<BEA-000365>

<Server state changed to ADMIN.>

<Mar 14,

2022

11:23:22,761

PM

IST>

<Notice>

<WebLogicServer>

<BEA-000365>

<Server state changed to RESUMING.>

<Mar 14,

2022

11:23:22,800

PM

IST>

<Notice>

<WebLogicServer>

<BEA-000329>

<Started the WebLogic Server Administration Server "AdminServer"™ for domain "AIMS domain” runn

ing in production mode.>

<Mar

2022

PM

IST>

<Notice>

14,

11:23:22,801

<Server> <BEA-002613> <Channel "Default” is now listening on ***********:7001 for protocols iiop,

t3,

ldap,

snmp,

http.>

<Mar

14,

2022

11:23:22,801

PM

IST>

<Notice>

<Server> <BEA-002613> <Channel "Default” is now listening on ***********:7001 for protocols iiop,

t3,

ldap,

snmp,

http.>

<Mar

14,

2022

11:23:22,828

PM

IST>

<Notice>

<WebLogicServer> <BEA-C00360> <The server started in RUNNING mode.>

<Mar

14,

2022

11:23:22,834

PM

IST>

<Notice>

<WebLogicServer> <BEA-000365> <Server state changed to RUNNING.>

i

[TEXT] 4.   Start Managed Server – 4 Managed Server’s

[TEXT] nohup ./startManagedWebLogic.sh Test1 http://weblogic-test:7001 &

[TEXT] nohup ./startManagedWebLogic.sh Test2 http://weblogic-test:7001 &

[TEXT] nohup ./startManagedWebLogic.sh FMSNEW http://weblogic-test:7001 &

[TEXT] nohup ./startManagedWebLogic.sh IDGTEST http://weblogic-test:7001 &

[IMAGE - Page 5]
Image saved: sequential_output\page_5_image_2.png
Size: (1366, 716)
OCR Text:
fig because ApplicationPath annotation is not set on it.>

<Mar 14,

2022 11:32:07,960 PM IST> <Warning> <JAXRSIntegration> <BEA-2192510> <Cannot add Jersey servlet for application class com.sun.jersey.api.core.ClasspathResource

Config because ApplicationPath annotation is not set on it.>

<Mar 14,

2022

11:32:07,960 PM

IST> <Warning> <JAXRSIntegration> <BEA-2192510> <Cannot add Jersey servlet for application class com.sun.jersey.api.core.ScanningResourceC

onfig because

ApplicationPath

annotation is

not set on it.>

2022

IST> <Notice>

<Mar 14,

11:32:14,677 PM

<Log Management> <BEA-170027> <The server has successfully established a connection with the Domain level Diagnostic Service

>

<Mar 14,

2022

11:32:15,306

PM

IST>

<Notice>

<WebLogicServer> <BEA-G00365> <Server state

changed to ADMIN.>

<Mar 14,

2022

11:32:15,357

PM

IST>

<Notice>

<WebLogicServer> <BEA-G00365> <Server state

changed to RESUMING.>

:

<Mar 14,

2022

11:32:15,412

PM

IST>

<Notice>

<JMX> <BEA-149535> <JMX Resiliency Activity

Server=IDGTEST

:

Received

a CONNECT EVENT

>

:

<Mar 14,

2022

11:32:15,412

PM

IST>

<Notice>

<JMX> <BEA-149535> <JMX Resiliency Activity

Server=IDGTEST

:

Starting JMX connection.

forceReconnect value:

false>

<Mar 14,

2022

11:32:15,423

PM

IST>

<Notice>

<Server> <BEA-G02613> <Channel "Default[5)" is now listening on 127.0.6.1:7005 for protocols iiop,

63,

ldap,

snmp,

http.>

<Mar 14,

2022

11:32:15,424

PM

IST>

<Notice>

<WebLogicServer> <BEA-000330> <Started the WebLogic Server Managed Server "IDGIEST” for domain "AIMS domain” running in prod

uction mode.>

<Mar 14,

2022

11:32:15,424

PM

IST>

<Notice>

<Server> <BEA-G02613> <Channel "Default[3)" is now listening

on 10.10.133.19:7005 for protocols iiop,

63,

ldap,

snmp,

http.>

<Mar 14,

2022

11:32:15,425

PM

IST>

<Notice>

<Server> <BEA-G02613> <Channel "Default[2)" is now listening

on 10.10.72.36:7005 for protocols iiop,

63,

ldap,

snmp,

http.>

<Mar 14,

2022

11:32:15,426

PM

IST>

<Notice>

<Server> <BEA-G02613> <Channel "Default[1)" is now listening

on 192.168.122.1:7005 for protocols iiop,

3,

ldap,

snmp,

http.

>

<Mar 14,

2022

11:32:15,426

PM

IST>

<Notice>

<WebLogicServer> <BEA-GO00360> <The server started in RUNNING

mode .>

<Mar 14,

2022

11:32:15,426

PM

IST>

<Notice>

on G6:0:0:0:0:0:0:1%10:7005 for protocols iiop,

63,

ldap,

snmp,

http.>

<Server> <BEA-G02613> <Channel merous is now listening

<Mar 14,

2022

11:32:15,427

PM

IST>

<Notice>

<Server>

<BEA-002613> <Channel

"Default” is

now listening on

16.10.96.14:7005 for protocols iiop,

63,

ldap,

snmp,

http.>

2022

PM

IST>

<Notice>

<Server>

<BEA-002613> <Channel

<Mar 14,

11:32:15,428

"Default [5]"

is now listening

on 127.0.0.1:7005 for protocols iiop,

3,

ldap,

snmp,

http.>

<Mar 14,

2022

11:32:15,429

PM

IST>

<Notice>

<Server>

<BEA-002613> <Channel

"Default [3]"

is now listening

on 10.10.133.19:7005 for protocols iiop,

63,

ldap,

snmp,

http.>

<Mar 14,

2022

11:32:15,432

PM

IST>

<Notice>

<Server>

<BEA-002613> <Channel

"Default [2]"

is now listening

on 10.10.72.36:7005 for protocols iiop,

63,

ldap,

snmp,

http.>

<Mar 14,

2022

11:32:15,433

PM

IST>

<Notice>

<WebLogicServer> <BEA-G00365> <Server state

changed to RUNNING.>

2022

PM

IST>

<Notice>

<Mar 14,

11:32:15,434

<Server> <BEA-G02613> <Channel "Default[1)”

is now listening on 192.168.122.1:7605 for protocols iiop,

63,

ldap,

snmp,

http.

>

<Mar 14,

2022

11:32:15,435

PM

IST>

<Notice>

<Server> <BEA-G02613> <Channel "Default [4])”

is now listening

on G6:0:0:0:0:0:0:1%10:7005 for protocols iiop,

63,

ldap,

snmp,

http.>

<Mar 14,

2022

11:32:15,436

PM

IST>

<Notice>

<Server> <BEA-002613> <Channel "Default" is

now listening on

16.10.96.14:7005 for protocols iiop,

63,

ldap,

snmp,

http.>

2022

PM

IST>

<Notice>

Server=IDGTEST

:

:

<Mar 14,

11:32:16,966

<JMX> <BEA-149535> <JMX Resiliency Activity

Registering call backs for server>

<Mar 14,

2022

11:32:16,967

PM

IST>

<Notice>

<JMX> <BEA-149535> <JMX Resiliency Activity

Server=IDGTEST

:

:

Initializing callbacks>

:

<Mar 14,

2022

11:32:16,968

PM

IST>

<Notice>

<JMX> <BEA-149535> <JMX Resiliency Activity

Server=IDGTEST

:

Added MBeanServerConnection in DomainRuntimeServiceMBean >

<Mar 14,

2022

11:32:16,969

PM

IST>

<Notice>

<JMX> <BEA-149535> <JMX Resiliency Activity

Server=IDGTEST

:

:

Callback is done>

<Mar 14,

2022

11:32:16,969

PM

IST>

<Warning> <JMX> <BEA-149506> <Established JMX Connectivity with IDGTEST at the JMX Service URL of service:jmx:t3://***********:7005/j

ndi/weblogic.management .mbeanservers.runtime.>

<Mar 14,

2022 11:32:16,975 PM IST> <Notice> <JMX> <BEA-149535> <JMX Resiliency Activity Server=IDGTEST

:

:

Server is reachable,

after a single attempt.

Will return from t

he retry.>


==================================================

==================== PAGE 6 ====================

[TEXT] 5.   Check Weblogic process status after starting to ensure weblogic processes are started.

[TEXT] ps -ef | grep wls

[TEXT] ps -ef | grep Node

[IMAGE - Page 6]
Image saved: sequential_output\page_6_image_1.png
Size: (1366, 485)
OCR Text:
-ef

[xoot@hyddrweblogic-test bin]J# ps

|

grep wls

root

17173

i

0 23:39 pts/O

00:00:01 /usr/java/jdk1.8.0_144/bin/java -Dderby.system.home=/Middleware/OracleHome/user projects/domains/AIMS domain/common/db —

classpath /Middleware/OracleHome/wiserver/common/derby/lib/derby. jar: /Middleware/OracleHome/wiserver/common/derby/lib/derbynet. jar: /Middleware/OracleHome/wiserver/commo

n/derby/lib/derbytools.jar: /Middleware/OracleHome/wiserver/common/derby/lib/derbyclient.jar org.apache.derby.drda.NetworkServerControl start

root

17186 17129 42 23:39 pts/0O

00:01:15 /usr/java/jdk1.8.0_144/bin/java -server -Xms256m -Xmx512m -cp /Middleware/OracleHome/wiserver/server/lib/weblogic—launch

ex.jar -Dlaunch.use.env.classpath=true -Dweblogic.Name=AdminServer -Djava.security.policy=/Middleware/OracleHome/wiserver/server/lib/weblogic.policy -Dweblogic. Producti

onModeEnabled=true -Djava.system.class.loader=com.oracle.classloader.weblogic.LaunchClassLoader -javaagent:/Middleware/OracleHome/wiserver/server/lib/debugpatch-agent.j

ax -da -Dwis.home=/Middleware/OracleHome/wiserver/server -Dweblogic.home=/Middleware/OracleHome/wiserver/server weblogic.Server

root

18329 18280 99 23:42 pts/O

00:00:53 /usr/java/jdk1.8.0_144/bin/java -server -Xms256m -Xmx51l2m -cp /Middleware/OracleHome/wiserver/server/lib/weblogic—launch

ex.jar -Dlaunch.use.env.classpath=true -Dweblogic.Name=FMSTEST -Djava.security.policy=/Middleware/OracleHome/wiserver/server/lib/weblogic.policy -Dweblogic.ProductionMo

deEnabled=true -Djava.system.class.loader=com.oracle.classloader.weblogic.LaunchClassLoader -javaagent:/Middleware/OracleHome/wiserver/server/lib/debugpatch-agent.jar -

da -Dwlis.home=/Middleware/OracleHome/wiserver/server -Dweblogic.home=/Middleware/OracleHome/wiserver/server -Dweblogic.management.server=http://weblogic-test:7001 weblo

gic.Server

root

18544 18543

@ 23:42 pts/O

00:00:00 /bin/sh /Middleware/OracleHome/wiserver/server/bin/startNodeManager.sh

root

18591 18544 58 23:42 pts/0O

00:00:05 /usr/java/jdk1.8.0_144/bin/java -server -Xms32m -Xmx200m -Djdk.tls.ephemeralDHKeySize=2048 -Dcoherence.home=/Middleware/

OracleHome/wiserver/../coherence —Dbea.home=/Middleware/OracleHome/wiserver/.. —-Dweblogic.RootDirectory—/Middleware/OracleHome/user_projects/domains/AIMS domain -Djava.

system.class.loader=com. oracle.classloader.weblogic.LaunchClassLoader -Djava.security.policy=/Middleware/OracleHome/wiserver/server/lib/weblogic.policy -Dweblogic.nodem

anager . JavaHome=/usr/java/jdk1.8.0_144 weblogic.NodeManager -v

root

18674 13152

@ 23:42 pts/O

GG:00:00 grep —--color=auto wis

[xoot@hyddrweblogic-test bin]J# ps

-ef

|

grep Node

root

18543 13152

@ 23:42 pts/O

00:00:00 /bin/sh ./startNodeManager.sh

root

18544 18543

@ 23:42 pts/O

00:00:00 /bin/sh /Middleware/OracleHome/wlserver/server/bin/startNodeManager.sh

root

18591 18544 40 23:42 pts/0O

00:00:05 /usr/java/jdk1.8.0_144/bin/java -server -Xms32m -Xmx200m -Djdk.tls.ephemeralDHKeySize=2048 -Dcoherence.home=/Middleware/

OracleHome/wlserver/../coherence -Dbea.home=/Middleware/OracleHome/wlserver/.. —Dwablogic.RootDirectory=/Middleware/OracleHome/user_projects/domains/AIMS domain -Djava.

system.class.loader=com.oracle.classloader.weblogic.LaunchClassLoader -Djava.secur

y-policy=/Middleware/OracleHome/wlserver/server/lib/weblogic.policy -Dweblogic.nodem

anager . JavaHome=/usr/java/jdk1.8.0_144 weblogic.NodeManager -v

root

18700 13152

@ 23:42 pts/O

GG:00:06 grep --color=auto Node

[root@hyddrweblogic-test bin]# I

[TEXT] ---------------------------------------------------------END----------------------------------------------------------------------


==================================================
