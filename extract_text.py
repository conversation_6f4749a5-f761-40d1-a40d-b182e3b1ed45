import paddle
print("GPU available:", paddle.device.is_compiled_with_cuda())




from paddleocr import PaddleOCR

# Initialize PaddleOCR with GPU usage enabled
ocr = PaddleOCR(use_gpu=True)  # Set use_gpu=True to enable GPU acceleration

# Path to your image
image_path = r'D:\AllPythonProjects\PythonProject\Paddle_OCR_Service\page_3_image_1.png'

# Perform OCR inference
result = ocr.ocr(image_path)

# Print the recognized text results
for line in result:
    print(line)
